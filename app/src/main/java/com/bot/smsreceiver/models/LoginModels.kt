package com.bot.smsreceiver.models

import kotlinx.serialization.Serializable

@Serializable
data class LoginRequest(
    val username: String,
    val password: String
)

@Serializable
data class Customer(
    val id: Int,
    val name: String,
    val url: String,
    val panel_subdomain_prefix: String,
    val bot_subdomain_prefix: String,
    val username: String,
    val is_active: Boolean,
    val created_at: String,
    val updated_at: String
)

@Serializable
data class LoginResponse(
    val message: String,
    val customer: Customer? = null
)
