package com.bot.smsreceiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.telephony.SmsMessage
import android.util.Log
import okhttp3.FormBody
import okhttp3.OkHttpClient
import okhttp3.Request
import io.ktor.client.*
import io.ktor.client.engine.okhttp.*
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import io.ktor.client.plugins.contentnegotiation.*
import kotlinx.serialization.json.Json
import kotlinx.serialization.Serializable
import io.ktor.serialization.kotlinx.json.*
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.json.JSONObject


public class SmsReceiver : BroadcastReceiver() {

    override fun onReceive(context: Context?, intent: Intent?) {
        if (context == null) return
        if (intent == null || intent.action != "android.provider.Telephony.SMS_RECEIVED") return

        // Check if user is logged in
        val sessionManager = SessionManager.getInstance(context)
        if (!sessionManager.isLoggedIn) {
            Log.d("SmsReceiver", "User not logged in, ignoring SMS")
            return
        }

        val bundle: Bundle? = intent.extras
        if (bundle != null) {
            val pdus = bundle.get("pdus") as? Array<*>
            if (pdus != null) {
                for (pdu in pdus) {
                    val format = bundle.getString("format")
                    val smsMessage = SmsMessage.createFromPdu(pdu as ByteArray, format)
                    val sender = smsMessage.displayOriginatingAddress
                    val messageBody = smsMessage.messageBody

                    if (sender != "Syriatel" && sender != "BBSF") return
                    if(!messageBody.startsWith("تم استلام") && !messageBody.startsWith("استلام")) return

                    val helper = Helper()
                    CoroutineScope(Dispatchers.IO).launch {
                        helper.sendPostRequest(context, sender, messageBody)
                    }
                }
            }
        }
    }





}
