package com.bot.smsreceiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.telephony.SmsMessage
import android.util.Log
import okhttp3.FormBody
import okhttp3.OkHttpClient
import okhttp3.Request
import io.ktor.client.*
import io.ktor.client.engine.okhttp.*
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import io.ktor.client.plugins.contentnegotiation.*
import kotlinx.serialization.json.Json
import kotlinx.serialization.Serializable
import io.ktor.serialization.kotlinx.json.*
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.json.JSONObject


public class SmsReceiver : BroadcastReceiver() {

    override fun onReceive(context: Context?, intent: Intent?) {
        if (context == null) return
        if (intent == null || intent.action != "android.provider.Telephony.SMS_RECEIVED") return

        // Check if user is logged in
        val sessionManager = SessionManager.getInstance(context)
        if (!sessionManager.isLoggedIn) {
            Log.d("SmsReceiver", "User not logged in, ignoring SMS")
            return
        }

        val bundle: Bundle? = intent.extras
        if (bundle != null) {
            val pdus = bundle.get("pdus") as? Array<*>
            if (pdus != null) {
                // Combine all PDUs into a single message to avoid duplicates
                val messages = mutableListOf<SmsMessage>()
                val format = bundle.getString("format")

                // Process all PDUs first
                for (pdu in pdus) {
                    val smsMessage = SmsMessage.createFromPdu(pdu as ByteArray, format)
                    messages.add(smsMessage)
                }

                // If we have messages, process only the first one (they should all be from the same sender)
                if (messages.isNotEmpty()) {
                    val firstMessage = messages[0]
                    val sender = firstMessage.displayOriginatingAddress

                    // Combine all message bodies in case the SMS was split across multiple PDUs
                    val fullMessageBody = messages.joinToString("") { it.messageBody }

                    Log.d("SmsReceiver", "Received SMS from: $sender, Message: $fullMessageBody")

                    // Check if sender is valid
                    if (sender != "Syriatel" && sender != "BBSF") {
                        Log.d("SmsReceiver", "Ignoring SMS from unknown sender: $sender")
                        return
                    }

                    // Check if message content is valid
                    if (!fullMessageBody.startsWith("تم استلام") && !fullMessageBody.startsWith("استلام")) {
                        Log.d("SmsReceiver", "Ignoring SMS with invalid content: $fullMessageBody")
                        return
                    }

                    Log.d("SmsReceiver", "Processing payment SMS from $sender")

                    // Send the request only once for the complete message
                    val helper = Helper()
                    CoroutineScope(Dispatchers.IO).launch {
                        helper.sendPostRequest(context, sender, fullMessageBody)
                    }
                }
            }
        }
    }





}
