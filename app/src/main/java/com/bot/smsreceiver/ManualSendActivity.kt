package com.bot.smsreceiver

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowForwardIos
import androidx.compose.material3.Button
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.material3.RadioButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TextField
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import com.bot.smsreceiver.ui.theme.SmsReceiverTheme
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch


@Suppress("DEPRECATION")
class ManualSendActivity : ComponentActivity(){
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent{
               ScreenBody { finish() }
        }
    }
    @OptIn(ExperimentalMaterial3Api::class)
    @Composable
    fun ScreenBody(onBackPressed: () -> Unit){
        SmsReceiverTheme {
            CompositionLocalProvider(LocalLayoutDirection provides LayoutDirection.Rtl) {
                Scaffold(
                    modifier = Modifier.padding(top = 20.dp),
                    topBar = {
                        Topbar{onBackPressed}
                    }
                ) { innerPadding ->
                    Box(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(innerPadding)
                    ) {
                        Options()
                    }
                }
            }
        }
    }
    @Composable
    fun Options(){
        var paymentMethod by remember { mutableStateOf("Syriatel") }
        var message by remember {mutableStateOf("")}
        var loading by remember { mutableStateOf(false) }
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            verticalArrangement = Arrangement.Top,
            horizontalAlignment = Alignment.Start
        ) {
            Text(text = "اختر طريقة الدفع:")

            Spacer(modifier = Modifier.height(8.dp))

            Row(verticalAlignment = Alignment.CenterVertically) {
                RadioButton(
                    selected = paymentMethod == "Syriatel",
                    onClick = { paymentMethod = "Syriatel" }
                )
                Text(text = "سيرياتل")
            }

            Row(verticalAlignment = Alignment.CenterVertically) {
                RadioButton(
                    selected = paymentMethod == "BBSF",
                    onClick = { paymentMethod = "BBSF" }
                )
                Text(text = "بيمو")
            }

            Spacer(modifier = Modifier.height(16.dp))
            TextField(
                value = message,
                onValueChange = { message = it },
                label = { Text("أدخل الرسالة هنا") },
                modifier = Modifier
                    .fillMaxWidth()
                    .height(150.dp), // ✅ Set height to allow multiple lines
                maxLines = Int.MAX_VALUE, // ✅ Allows unlimited lines
                singleLine = false, // ✅ Allows multi-line input
                colors = OutlinedTextFieldDefaults.colors(
                    focusedBorderColor = Color.Blue,
                    unfocusedBorderColor = Color.Gray,
                    cursorColor = Color.Red
                ),
                shape = MaterialTheme.shapes.medium
            )
            Spacer(modifier = Modifier.height(16.dp))
            Button(
                modifier = Modifier.fillMaxWidth(),
                enabled = !loading,
                onClick = {
                    if(!loading){
                        loading = true
                        val helper = Helper()
                        val currentContext = this@ManualSendActivity
                        CoroutineScope(Dispatchers.IO).launch {
                            helper.sendPostRequest(currentContext, paymentMethod, message)
                            kotlinx.coroutines.delay(2000) // Simulate network call
                            loading = false
                            message = ""
                        }
                    }
                }
            ) {
                if(loading){
                    CircularProgressIndicator(
                        modifier = Modifier
                            .size(24.dp), // ✅ Small size inside the button
                        color = Color.White, // ✅ Matches button text color
                        strokeWidth = 2.dp
                    )
                }
                else{
                    Text("إرسال")
                }

            }
        }
    }
    @OptIn(ExperimentalMaterial3Api::class)
    @Composable
    fun Topbar(onBackPressed: () -> Unit) {
        TopAppBar(
            title = {
                Text(
                    "رفع رسالة يدوي",
                )
            },
            navigationIcon = {
                IconButton(onClick = onBackPressed) {
                    Icon(
                        imageVector = Icons.Default.ArrowForwardIos,
                        contentDescription = "رجوع"
                    )
                }
            },

            )
    }


    @Preview(showBackground = true)
    @Composable
    fun DefaultPreview() {
        ScreenBody { finish() }
    }
}