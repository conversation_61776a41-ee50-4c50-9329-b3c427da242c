package com.bot.smsreceiver

import android.content.Context
import android.util.Log
import com.bot.smsreceiver.models.LoginRequest
import com.bot.smsreceiver.models.LoginResponse
import io.ktor.client.HttpClient
import io.ktor.client.engine.okhttp.OkHttp
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.client.request.accept
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.client.statement.HttpResponse
import io.ktor.client.statement.bodyAsText
import io.ktor.http.ContentType
import io.ktor.http.HttpStatusCode
import io.ktor.http.contentType
import io.ktor.serialization.kotlinx.json.json
import kotlinx.serialization.json.Json
import org.json.JSONObject
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlin.random.Random

class ApiService(private val context: Context) {
    private val json = Json {
        prettyPrint = true
        isLenient = true
        ignoreUnknownKeys = true
    }

    private val client = HttpClient(OkHttp) {
        install(ContentNegotiation) {
            json(json)
        }
    }

    companion object {
        // Default login API endpoint
        private const val LOGIN_API_URL = "https://customers.skilltone-dev.com/api/login"

        @Volatile
        private var instance: ApiService? = null

        fun getInstance(context: Context): ApiService {
            return instance ?: synchronized(this) {
                instance ?: ApiService(context).also { instance = it }
            }
        }
    }

    suspend fun login(username: String, password: String): Result<LoginResponse> {
        return withContext(Dispatchers.IO) {
            try {
                val loginRequest = LoginRequest(username, password)

                val response: HttpResponse = client.post(LOGIN_API_URL) {
                    contentType(ContentType.Application.Json)
                    accept(ContentType.Application.Json)
                    setBody(loginRequest)
                }

                val responseBody = response.bodyAsText()
                Log.d("ApiService", "Login response: $responseBody")

                try {
                    // Parse the response
                    val loginResponse = json.decodeFromString<LoginResponse>(responseBody)

                    if (response.status == HttpStatusCode.OK) {
                        // Check if customer data is available in the response
                        if (loginResponse.customer != null) {
                            // Extract the API URL from the customer data
                            val apiUrl = loginResponse.customer.url

                            // We don't have a token in this response, but we can use the customer ID as a simple token
                            val customerId = loginResponse.customer.id.toString()

                            // Save the full customer object along with other session data
                            SessionManager.getInstance(context).createLoginSession(
                                customerId,
                                apiUrl,
                                loginResponse.customer.username,
                                loginResponse.customer
                            )

                            Log.d("ApiService", "Login successful. Customer: ${loginResponse.customer.name}, API URL: $apiUrl")

                            // Return success with the login response
                            Result.success(loginResponse)
                        } else {
                            // This should not happen with a successful response, but handle it just in case
                            Log.e("ApiService", "Login response missing customer data: $responseBody")
                            Result.failure(Exception("بيانات المستخدم غير متوفرة"))
                        }
                    } else {
                        // Handle error response with message
                        val errorMessage = loginResponse.message.takeIf { it.isNotBlank() } ?: "Unknown error"
                        Log.e("ApiService", "Login error: ${response.status}, $errorMessage")
                        Result.failure(Exception(errorMessage))
                    }
                } catch (e: Exception) {
                    // Handle parsing error or other exceptions
                    Log.e("ApiService", "Error parsing login response: ${e.message}")

                    // Try to extract error message from response if possible
                    val errorMessage = try {
                        val errorJson = org.json.JSONObject(responseBody)
                        errorJson.optString("message", "Unknown error")
                    } catch (jsonEx: Exception) {
                        "Unknown error"
                    }

                    Result.failure(Exception(errorMessage))
                }
            } catch (e: Exception) {
                Log.e("ApiService", "Login exception: ${e.message}")
                Result.failure(e)
            }
        }
    }

    suspend fun sendPaymentRequest(sender: String, message: String): Result<String> {
        return withContext(Dispatchers.IO) {
            try {
                // Get the API URL from the saved session data
                val sessionManager = SessionManager.getInstance(context)
                val apiUrl = sessionManager.apiUrl

                // Check if we have a valid API URL
                if (apiUrl.isNullOrEmpty()) {
                    Log.e("ApiService", "API URL not available. User needs to login first.")
                    return@withContext Result.failure(Exception("API URL not available. Please login first."))
                }

                Log.d("ApiService", "Using API URL from saved session: $apiUrl")

                val delayTime = Random.nextLong(500, 2000) // random delay between 500ms and 2000ms
                kotlinx.coroutines.delay(delayTime)

                val helper = Helper()
                val info = helper.getMessageInfo(sender, message)

                // Define the request payload
                val requestBody = JSONObject().apply {
                    put("payment_info", info.paymentInfo)
                    put("provider", info.paymentMethod)
                    put("amount", info.amount)
                }.toString()

                // Send POST request to the API URL from the customer data
                // The URL already contains the full domain, so we just need to append the endpoint
                // Using api/v1/payment as specified
                val fullUrl = "$apiUrl/api/v1/payment"
                Log.d("ApiService", "Sending payment request to: $fullUrl")
                Log.d("ApiService", "Payment payload: $requestBody")

                val response: HttpResponse = client.post(fullUrl) {
                    contentType(ContentType.Application.Json)
                    accept(ContentType.Application.Json)
                    setBody(requestBody)
                }

                // Handle the response
                if (response.status == HttpStatusCode.OK) {
                    val responseBody = response.bodyAsText()
                    Log.d("ApiService", "Payment success: $responseBody")
                    Result.success(responseBody)
                } else {
                    val errorBody = response.bodyAsText()
                    Log.e("ApiService", "Payment error: ${response.status}, $errorBody")
                    Result.failure(Exception("Payment failed: ${response.status}"))
                }
            } catch (e: Exception) {
                Log.e("ApiService", "Payment exception: ${e.message}")
                Result.failure(e)
            }
        }
    }

    fun close() {
        client.close()
    }
}
