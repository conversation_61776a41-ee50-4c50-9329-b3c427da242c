package com.bot.smsreceiver

import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.material3.Text
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import com.bot.smsreceiver.ui.theme.SmsReceiverTheme
import android.Manifest
import android.annotation.SuppressLint
import androidx.compose.ui.text.TextStyle
import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.net.Uri
import android.os.PowerManager
import android.provider.Settings
import android.widget.Toast
import androidx.compose.foundation.layout.Box
import androidx.compose.ui.Alignment
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.sp
import android.app.ActivityManager
import android.util.Log
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Button
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp


class MainActivity : ComponentActivity() {
    private val smsPermissionCode = 100
    private lateinit var sessionManager: SessionManager

    @SuppressLint("UnusedMaterial3ScaffoldPaddingParameter")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        // Initialize SessionManager
        sessionManager = SessionManager.getInstance(this)

        setContent {
            AppContent()
        }

        val powerManager = getSystemService(POWER_SERVICE) as PowerManager

        if (!powerManager.isIgnoringBatteryOptimizations(packageName)) {
            requestIgnoreBatteryOptimizations()
        } else {
            Toast.makeText(this, "Battery optimization already disabled", Toast.LENGTH_SHORT).show()
        }

        if(!hasSmsPermission()){
            requestSmsPermission()
        }

        // Check if user is already logged in from a previous session
        if (sessionManager.isLoggedIn) {
            Log.d("MainActivity", "User already logged in: ${sessionManager.username}")

            // Verify that we have the necessary data
            val customer = sessionManager.customer
            val apiUrl = sessionManager.apiUrl

            if (customer != null && !apiUrl.isNullOrEmpty()) {
                Log.d("MainActivity", "Restored session for customer: ${customer.name}, API URL: $apiUrl")
                // Start the SMS service since we're already logged in
                startSmsService()
            } else {
                Log.e("MainActivity", "Incomplete session data, logging out")
                // If we have incomplete data, log out to reset the session
                sessionManager.logout()
            }
        } else {
            Log.d("MainActivity", "No active session found")
        }
    }

    @Composable
    fun AppContent() {
        var isLoggedIn by remember { mutableStateOf(sessionManager.isLoggedIn) }

        SmsReceiverTheme {
            if (isLoggedIn) {
                MainScreen(
                    onLogout = {
                        // Stop the SMS service before logging out
                        stopSmsService()

                        // Clear session data
                        sessionManager.logout()
                        isLoggedIn = false
                    }
                )
            } else {
                LoginScreen(
                    onLoginSuccess = {
                        // Update the login state
                        isLoggedIn = true

                        // Start the SMS service now that the user is logged in
                        startSmsService()

                        // Show a welcome toast
                        sessionManager.username?.let { username ->
                            Toast.makeText(
                                this@MainActivity,
                                "مرحباً $username",
                                Toast.LENGTH_SHORT
                            ).show()
                        }
                    }
                )
            }
        }
    }

    @Composable
    fun MainScreen(onLogout: () -> Unit) {
        val context = LocalContext.current
        var message = "خدمة التحقق من الرسائل لاتعمل"
        val manualSendActivity = Intent(context, ManualSendActivity::class.java)

        if (isServiceRunning(context, SmsForegroundService::class.java)) {
            message = "خدمة التحقق من الرسائل تعمل في الخلفية"
        }

        Box(
            modifier = Modifier
                .fillMaxWidth()
                .fillMaxHeight(),
            contentAlignment = Alignment.Center,
        ) {
            Column(horizontalAlignment = Alignment.CenterHorizontally) {
                // Show customer information
                sessionManager.customer?.let { customer ->
                    Text(
                        "مرحباً ${customer.name}",
                        fontSize = 20.sp,
                        textAlign = TextAlign.Center,
                        color = MaterialTheme.colorScheme.primary
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    Text(
                        "اسم المستخدم: ${customer.username}",
                        fontSize = 16.sp,
                        textAlign = TextAlign.Center,
                    )

                    Spacer(modifier = Modifier.height(16.dp))
                }

                Text(
                    message,
                    fontSize = 17.sp,
                    textAlign = TextAlign.Center,
                )

                Spacer(modifier = Modifier.height(16.dp))

                Button(
                    onClick = { context.startActivity(manualSendActivity) }
                ) {
                    Text("رفع رسالة يدوي")
                }

                Spacer(modifier = Modifier.height(16.dp))

                Button(
                    onClick = onLogout
                ) {
                    Text("تسجيل الخروج")
                }
            }
        }
    }
    fun isServiceRunning(context: Context, serviceClass: Class<*>): Boolean {
        val activityManager = context.getSystemService(ACTIVITY_SERVICE) as ActivityManager
        @Suppress("DEPRECATION")
        val runningServices = activityManager.getRunningServices(Int.MAX_VALUE)
        return runningServices.any { it.service.className == serviceClass.name }
    }
    @SuppressLint("BatteryLife")
    private fun requestIgnoreBatteryOptimizations() {
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            try {
                val intent = Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS).apply {
                    data = Uri.parse("package:$packageName")
                }
                startActivity(intent)
            } catch (e: Exception) {
                Log.d("sms request","${e.localizedMessage}")
                Toast.makeText(this, "Error requesting battery optimization permission", Toast.LENGTH_SHORT).show()
            }
//        }
    }
    @SuppressLint("InlinedApi")
    private fun hasSmsPermission(): Boolean {
        return ContextCompat.checkSelfPermission(this, Manifest.permission.RECEIVE_SMS) == PackageManager.PERMISSION_GRANTED &&
                ContextCompat.checkSelfPermission(this, Manifest.permission.READ_SMS) == PackageManager.PERMISSION_GRANTED &&
                ContextCompat.checkSelfPermission(this,Manifest.permission.POST_NOTIFICATIONS) == PackageManager.PERMISSION_GRANTED
    }

    @SuppressLint("InlinedApi")
    private fun requestSmsPermission() {
        ActivityCompat.requestPermissions(
            this,
            arrayOf(Manifest.permission.RECEIVE_SMS, Manifest.permission.READ_SMS,Manifest.permission.POST_NOTIFICATIONS),
            smsPermissionCode
        )

    }
    fun isInternetAvailable(context: Context): Boolean {
        val connectivityManager = context.getSystemService(CONNECTIVITY_SERVICE) as ConnectivityManager

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            val network = connectivityManager.activeNetwork ?: return false
            val activeNetwork = connectivityManager.getNetworkCapabilities(network) ?: return false
            return when {
                activeNetwork.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> true
                activeNetwork.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> true
                activeNetwork.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) -> true
                else -> false
            }
        } else {
            @Suppress("DEPRECATION")
            val networkInfo = connectivityManager.activeNetworkInfo
            return networkInfo != null && networkInfo.isConnected
        }
    }
//    @Deprecated
//    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<out String>, grantResults: IntArray) {
//        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
//        if (requestCode == SMS_PERMISSION_CODE && grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
//            startSmsService()
//        }
//    }
    private fun startSmsService() {
        val serviceIntent = Intent(this, SmsForegroundService::class.java)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
              startForegroundService(serviceIntent)
        } else {
            startService(serviceIntent)
        }
        Log.d("MainActivity", "SMS service started")
    }

    private fun stopSmsService() {
        val serviceIntent = Intent(this, SmsForegroundService::class.java)
        stopService(serviceIntent)
        Log.d("MainActivity", "SMS service stopped")
    }
    @Composable
    @Preview
    fun Preview(){
        AppContent()
    }
}
