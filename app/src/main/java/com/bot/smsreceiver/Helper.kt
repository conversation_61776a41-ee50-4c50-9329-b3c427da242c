package com.bot.smsreceiver

import android.content.Context
import android.util.Log
import kotlinx.serialization.Serializable
import org.json.JSONObject
import kotlinx.coroutines.*

class Helper {
    fun getMessageInfo(sender:String, message:String): MessageInfo {
        val msg = message.replace("ل.س","")
        val parts = msg.split(" ")
        var paymentMethod = ""
        var amount = ""
        var paymentInfo = ""
        val nums = parts.filter { it.toDoubleOrNull() != null }
        val paymentNumber = parts[parts.size-1]
        if (sender == "Syriatel") {
            amount = nums[0]
            paymentInfo = nums[1]
            paymentMethod = "syriatel_cash"
        } else {
            amount = nums[0]
            paymentMethod = "bemo"
            paymentInfo = paymentNumber.split("-")[0]
        }
        Log.d("sms request", amount)
        Log.d("sms request", paymentInfo)
        Log.d("sms request", paymentMethod)
        return MessageInfo(
            paymentInfo = paymentInfo,
            amount = amount.toDouble(),
            paymentMethod = paymentMethod
        )
    }

    suspend fun sendPostRequest(context: Context, sender: String, message: String) {
        val apiService = ApiService.getInstance(context)
        val result = apiService.sendPaymentRequest(sender, message)

        result.fold(
            onSuccess = { response ->
                Log.d("sms request", "Success: $response")
            },
            onFailure = { exception ->
                Log.d("sms request", "Request failed: ${exception.localizedMessage}")
            }
        )
    }

    // For backward compatibility
    suspend fun sendPostRequest(sender: String, message: String) {
        Log.e("Helper", "Using deprecated method without context. This may not work properly.")
    }

    @Serializable
    data class MessageInfo(
        val paymentInfo:String,
        val amount: Double,
        val paymentMethod:String
    )
    @Serializable
    data class RequestInfo(
        val paymentInfo: String,
        val amount: String,
        val provider:String
    )
}