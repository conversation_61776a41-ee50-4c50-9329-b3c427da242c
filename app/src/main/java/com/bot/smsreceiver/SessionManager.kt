package com.bot.smsreceiver

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import com.bot.smsreceiver.models.Customer
import kotlinx.serialization.encodeToString
import kotlinx.serialization.decodeFromString
import kotlinx.serialization.json.Json

class SessionManager(context: Context) {
    private var prefs: SharedPreferences = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)
    private var editor: SharedPreferences.Editor = prefs.edit()
    private val json = Json { ignoreUnknownKeys = true }

    companion object {
        private const val PREF_NAME = "SmsReceiverPrefs"
        private const val IS_LOGGED_IN = "IsLoggedIn"
        private const val KEY_TOKEN = "token"
        private const val KEY_API_URL = "apiUrl"
        private const val KEY_USERNAME = "username"
        private const val KEY_CUSTOMER_DATA = "customerData"

        @Volatile
        private var instance: SessionManager? = null

        fun getInstance(context: Context): SessionManager {
            return instance ?: synchronized(this) {
                instance ?: SessionManager(context).also { instance = it }
            }
        }
    }

    fun createLoginSession(token: String, apiUrl: String, username: String, customer: Customer) {
        editor.putBoolean(IS_LOGGED_IN, true)
        editor.putString(KEY_TOKEN, token)
        editor.putString(KEY_API_URL, apiUrl)
        editor.putString(KEY_USERNAME, username)

        // Save the entire customer object as a JSON string
        val customerJson = json.encodeToString(customer)
        editor.putString(KEY_CUSTOMER_DATA, customerJson)

        editor.apply()

        Log.d("SessionManager", "Login session created for $username with API URL: $apiUrl")
    }

    fun logout() {
        editor.clear()
        editor.apply()
        Log.d("SessionManager", "User logged out")
    }

    val isLoggedIn: Boolean
        get() = prefs.getBoolean(IS_LOGGED_IN, false)

    val token: String?
        get() = prefs.getString(KEY_TOKEN, null)

    val apiUrl: String?
        get() = prefs.getString(KEY_API_URL, null)

    val username: String?
        get() = prefs.getString(KEY_USERNAME, null)

    val customer: Customer?
        get() {
            val customerJson = prefs.getString(KEY_CUSTOMER_DATA, null)
            return if (customerJson != null) {
                try {
                    json.decodeFromString<Customer>(customerJson)
                } catch (e: Exception) {
                    Log.e("SessionManager", "Error decoding customer data: ${e.message}")
                    null
                }
            } else {
                null
            }
        }
}
